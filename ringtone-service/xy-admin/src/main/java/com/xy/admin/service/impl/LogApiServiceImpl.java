package com.xy.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xy.admin.entity.LogApi;
import com.xy.admin.service.LogApiService;
import com.xy.admin.mapper.LogApiMapper;
import com.xy.admin.vo.logApi.LogApiQueryVO;
import com.xy.admin.vo.logApi.LogApiVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 针对表【log_api】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Service
public class LogApiServiceImpl extends ServiceImpl<LogApiMapper, LogApi>
        implements LogApiService {

    @Override
    public IPage<LogApiVO> queryPage(LogApiQueryVO queryVO) {
        // 构建查询条件
        LambdaQueryWrapper<LogApi> wrapper = new LambdaQueryWrapper<>();

        // 手机号查询
        if (StrUtil.isNotBlank(queryVO.getPhone())) {
            wrapper.like(LogApi::getPhone, queryVO.getPhone());
        }

        // 日志类型查询
        if (queryVO.getType() != null) {
            wrapper.eq(LogApi::getType, queryVO.getType());
        }

        // URL查询
        if (StrUtil.isNotBlank(queryVO.getUrl())) {
            wrapper.like(LogApi::getUrl, queryVO.getUrl());
        }

        // 请求IP查询
        if (StrUtil.isNotBlank(queryVO.getIp())) {
            wrapper.like(LogApi::getIp, queryVO.getIp());
        }

        // 方法类型查询
        if (StrUtil.isNotBlank(queryVO.getMethod())) {
            wrapper.like(LogApi::getMethod, queryVO.getMethod());
        }

        // 代码请求参数
        if (StrUtil.isNotBlank(queryVO.getRequest())) {
            wrapper.like(LogApi::getPosition, queryVO.getRequest());
        }

        // 代码响应内容
        if (queryVO.getRequest() != null) {
            wrapper.eq(LogApi::getResponse, queryVO.getResponse());
        }

        // 花费时间范围查询
        if (queryVO.getTimingMin() != null) {
            wrapper.ge(LogApi::getTiming, queryVO.getTimingMin());
        }
        if (queryVO.getTimingMax() != null) {
            wrapper.le(LogApi::getTiming, queryVO.getTimingMax());
        }

        // 创建时间范围查询
        if (queryVO.getCreateTimeStart() != null) {
            wrapper.ge(LogApi::getCreateTime, queryVO.getCreateTimeStart());
        }
        if (queryVO.getCreateTimeEnd() != null) {
            wrapper.le(LogApi::getCreateTime, queryVO.getCreateTimeEnd());
        }

        // 通用关键字查询（如果有keyword，则在多个字段中搜索）
        if (StrUtil.isNotBlank(queryVO.getKeyword())) {
            wrapper.and(w -> w
                    .like(LogApi::getUrl, queryVO.getKeyword())
                    .or().like(LogApi::getMethod, queryVO.getKeyword())
                    .or().like(LogApi::getIp, queryVO.getKeyword())
                    .or().like(LogApi::getPosition, queryVO.getKeyword())
                    .or().like(LogApi::getPhone, queryVO.getKeyword())
                    .or().like(LogApi::getExceptionName, queryVO.getKeyword())
            );
        }

        // 排序处理
        if (StrUtil.isNotBlank(queryVO.getSortField())) {
            boolean isAsc = "asc".equalsIgnoreCase(queryVO.getSortOrder());
            switch (queryVO.getSortField()) {
                case "createTime":
                    wrapper.orderBy(true, isAsc, LogApi::getCreateTime);
                case "ip":
                    wrapper.orderBy(true, isAsc, LogApi::getIp);
                    break;
                case "timing":
                    wrapper.orderBy(true, isAsc, LogApi::getTiming);
                    break;
                case "type":
                    wrapper.orderBy(true, isAsc, LogApi::getType);
                    break;
                case "method":
                    wrapper.orderBy(true, isAsc, LogApi::getMethod);
                    break;
                default:
                    wrapper.orderByDesc(LogApi::getCreateTime);
                    break;
            }
        } else {
            // 默认按创建时间倒序
            wrapper.orderByDesc(LogApi::getCreateTime);
        }

        // 分页查询
        Page<LogApi> page = queryVO.buildPage();
        IPage<LogApi> pageResult = this.page(page, wrapper);

        // 转换为VO
        List<LogApiVO> voList = pageResult.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建返回结果
        Page<LogApiVO> voPage = new Page<>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal());
        voPage.setRecords(voList);

        return voPage;
    }

    /**
     * 实体转VO
     */
    private LogApiVO convertToVO(LogApi entity) {
        LogApiVO vo = new LogApiVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}




